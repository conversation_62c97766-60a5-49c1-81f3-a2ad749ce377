import { WorkerEntrypoint } from 'cloudflare:workers';

import { PDF } from './classes/pdf';
import { Storage } from './classes/storage';
import { Template } from './classes/template';

export default class extends WorkerEntrypoint {
  async fetch() {
    return new Response('OK');
  }

  async signDocument() {
    const env = this.env;

    const html = Template.fill({
      template: 'signed-funding-application',
      data: { foo: 'bar' },
      env,
    });

    const pdf = await PDF.generate(html, env);
    return pdf;
  }
}
