const exampleData = {
  uuid: `${crypto.randomUUID()}`,
  signed_at: new Date().toISOString(),
  applicationFields: {
    businessName: 'Test LLC',
    dbaName: 'DBA',
    website: 'example.com',
    entityType: 'LLC',
    ein: '555555555',
    industry: 'Construction & Real Estate',
    businessStartDate: '1994-10-30',
    businessPhone: '***********',
    businessEmail: '<EMAIL>',
    address: {
      line1: '3222 Northwest Loop 410',
      line2: 'Address Line 2',
      city: 'San Antonio',
      state: 'TX',
      zip: '78213',
    },
    owners: [
      {
        firstName: 'Test',
        lastName: 'User',
        dateOfBirth: '1994-10-30',
        ssn: '555555555',
        phone: '***********',
        email: '<EMAIL>',
        address: {
          line1: '3222 Northwest Loop 410',
          line2: 'Address Line 2',
          city: 'San Antonio',
          state: 'TX',
          zip: '78213',
        },
        ownershipPercentage: 65,
        creditScore: '580-629',
      },
      {
        firstName: 'Test',
        lastName: 'User',
        dateOfBirth: '1994-10-30',
        ssn: '555555555',
        phone: '***********',
        email: '<EMAIL>',
        address: {
          line1: '3222 Northwest Loop 410',
          line2: 'Address Line 2',
          city: 'San Antonio',
          state: 'TX',
          zip: '78213',
        },
        ownershipPercentage: 35,
      },
    ],
    signature: `data:image/png;base64,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`,
  },
};

export class Template {
  static fill({ template, data, env }) {
    if (!env) throw new Error('missing env');
    console.log(`Generating template: ${template} with data:`, data);
    // TODO: Implement actual template generation logic
    const html = '<h1 style="color:blue;">Hello, World!</h1>';
    return html;
  }
}

const signedFundingApplicationTemplate = (data) => {
  const si
  return `<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Business Loan Application</title>
    <style>
      @page {
        size: A4;
        margin: 0.4in;
      }

      * {
        box-sizing: border-box;
      }

      body {
        font-family: 'Segoe UI', 'Arial', sans-serif;
        font-size: 12pt;
        line-height: 1.25;
        color: #333;
        margin: 0;
        padding: 0;
        background: white;
      }

      .document {
        max-width: 8.5in;
        margin: 0 auto;
        background: white;
      }

      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 2px solid #000;
        padding-bottom: 6px;
        margin-bottom: 10px;
      }

      .logo {
        height: 35px;
      }

      .company-info {
        text-align: right;
        font-size: 10pt;
        line-height: 1.25;
        color: #000;
      }

      .company-info strong {
        font-size: 11pt;
        color: #000;
      }

      .document-title {
        text-align: center;
        font-size: 16pt;
        font-weight: 600;
        margin: 6px 0 10px 0;
        color: #000;
        letter-spacing: 0.3px;
      }

      .section {
        margin-bottom: 10px;
      }

      .section-title {
        font-size: 11pt;
        font-weight: 600;
        background: #f5f5f5;
        color: #000;
        padding: 4px 8px;
        border-left: 3px solid #000;
        margin-bottom: 8px;
        border-radius: 2px;
      }

      .field-row {
        display: flex;
        margin-bottom: 3px;
        align-items: baseline;
      }

      .field-label {
        font-weight: 500;
        min-width: 85px;
        margin-right: 8px;
        font-size: 9pt;
        color: #000;
      }

      .field-value {
        flex: 1;
        border-bottom: 1px solid #000;
        padding-bottom: 2px;
        min-height: 12px;
        font-size: 10pt;
        color: #000;
      }

      .owner-section {
        border: 1px solid #000;
        border-radius: 4px;
        padding: 8px;
        margin-bottom: 8px;
        background: #f9f9f9;
      }

      .owner-title {
        font-weight: 600;
        font-size: 10pt;
        margin-bottom: 3px;
        color: #000;
        border-bottom: 1px solid #000;
        padding-bottom: 3px;
      }

      .signature-section {
        margin-top: 10px;
      }

      .signature-block {
        border: 1px solid #000;
        border-radius: 4px;
        padding: 12px;
        margin-bottom: 8px;
        background: #f9f9f9;
      }

      .signature-block p {
        margin: 5px 0;
      }

      .signature-image {
        max-width: 250px;
        max-height: 120px;
        border: 1px solid #000;
        border-radius: 2px;
        margin: 5px 0 0;
      }

      .signature-line {
        border-bottom: 1px solid #000;
        width: 250px;
        display: inline-block;
      }

      .footer {
        margin-top: 8px;
        padding-top: 4px;
        border-top: 1px solid #000;
        font-size: 8pt;
        text-align: center;
        color: #000;
      }

      .two-column {
        display: flex;
        gap: 10px;
      }

      .column {
        flex: 1;
      }

      .three-column {
        display: flex;
        gap: 6px;
      }

      .three-column .column {
        flex: 1;
      }

      @media print {
        body {
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
        }
      }
    </style>
  </head>
  <body>
    <div class="document">
      <!-- Header -->
      <div class="header">
        <img src="https://static.pinnaclefunding.com/app-logo.svg" alt="Pinnacle Funding" class="logo" />
        <div class="company-info">
          <strong>Pinnacle Funding Co.</strong><br />
          Phone: +****************<br />
          Email: <EMAIL><br />
          Website: https://pinnaclefunding.com/
        </div>
      </div>

      <!-- Document Title -->
      <div class="document-title">Business Loan Application</div>

      <!-- Application UUID -->
      <div style="font-size: 9pt; margin-bottom: 8px; color: #000">Application ID: <b>{{uuid}}</b></div>

      <!-- Business Information Section -->
      <div class="section">
        <div class="section-title">Business Information</div>

        <div class="two-column">
          <div class="column">
            <div class="field-row">
              <div class="field-label">Business Name:</div>
              <div class="field-value">{{businessName}}</div>
            </div>

            {{#dbaName}}
            <div class="field-row">
              <div class="field-label">DBA Name:</div>
              <div class="field-value">{{dbaName}}</div>
            </div>
            {{/dbaName}}

            <div class="field-row">
              <div class="field-label">Entity Type:</div>
              <div class="field-value">{{entityType}}</div>
            </div>

            <div class="field-row">
              <div class="field-label">EIN:</div>
              <div class="field-value">{{ein}}</div>
            </div>

            <div class="field-row">
              <div class="field-label">Industry:</div>
              <div class="field-value">{{industry}}</div>
            </div>
          </div>

          <div class="column">
            <div class="field-row">
              <div class="field-label">Start Date:</div>
              <div class="field-value">{{businessStartDate}}</div>
            </div>

            <div class="field-row">
              <div class="field-label">Phone:</div>
              <div class="field-value">{{businessPhone}}</div>
            </div>

            <div class="field-row">
              <div class="field-label">Email:</div>
              <div class="field-value">{{businessEmail}}</div>
            </div>

            {{#website}}
            <div class="field-row">
              <div class="field-label">Website:</div>
              <div class="field-value">{{website}}</div>
            </div>
            {{/website}}

            <div class="field-row">
              <div class="field-label">Address:</div>
              <div class="field-value">{{address.line1}}</div>
            </div>
            {{#address.line2}}
            <div class="field-row">
              <div class="field-label"></div>
              <div class="field-value">{{address.line2}}</div>
            </div>
            {{/address.line2}}
            <div class="field-row">
              <div class="field-label"></div>
              <div class="field-value">{{address.city}}, {{address.state}} {{address.zip}}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Owner Information Section -->
      <div class="section">
        <div class="section-title">Owner Information</div>

        {{#owners}}
        <div class="owner-section">
          <div class="owner-title">Owner {{@index_1}}</div>

          <div class="two-column">
            <div class="column">
              <div class="field-row">
                <div class="field-label">Name:</div>
                <div class="field-value">{{firstName}} {{lastName}}</div>
              </div>

              <div class="field-row">
                <div class="field-label">DOB:</div>
                <div class="field-value">{{dateOfBirth}}</div>
              </div>

              <div class="field-row">
                <div class="field-label">Email:</div>
                <div class="field-value">{{email}}</div>
              </div>

              <div class="field-row">
                <div class="field-label">Phone:</div>
                <div class="field-value">{{phone}}</div>
              </div>
            </div>

            <div class="column">
              <div class="field-row">
                <div class="field-label">Ownership:</div>
                <div class="field-value">{{ownershipPercentage}}%</div>
              </div>

              {{#creditScore}}
              <div class="field-row">
                <div class="field-label">Credit Score:</div>
                <div class="field-value">{{creditScore}}</div>
              </div>
              {{/creditScore}}

              <div class="field-row">
                <div class="field-label">Address:</div>
                <div class="field-value">{{address.line1}}</div>
              </div>
              {{#address.line2}}
              <div class="field-row">
                <div class="field-label"></div>
                <div class="field-value">{{address.line2}}</div>
              </div>
              {{/address.line2}}
              <div class="field-row">
                <div class="field-label"></div>
                <div class="field-value">{{address.city}}, {{address.state}} {{address.zip}}</div>
              </div>
            </div>
          </div>
        </div>
        {{/owners}}
      </div>

      <!-- Signature Section -->
      <div class="signature-section">
        <div class="section-title">Signature</div>

        <div class="signature-block">
          <div class="two-column">
            <div class="column">
              <p><strong>Applicant Signature:</strong></p>
              {{#signature}}
              <img src="{{signature}}" alt="Signature" class="signature-image" />
              {{/signature}} {{^signature}}
              <div class="signature-line"></div>
              {{/signature}}
            </div>
            <div class="column">
              <div class="field-row">
                <div class="field-label">Date Signed:</div>
                <div class="field-value">{{signedDate}}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer">
        <p>This document was digitally signed and electronically generated on {{signedDate}}</p>
        <p>Pinnacle Funding Co. | Phone: +**************** | Email: <EMAIL></p>
      </div>
    </div>
  </body>
</html>
`;
};
